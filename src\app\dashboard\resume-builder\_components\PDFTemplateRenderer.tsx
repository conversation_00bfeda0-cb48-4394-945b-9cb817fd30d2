import React from 'react';

// This function will be called dynamically, so we need to import React PDF components here
export function createPDFDocument(data: any) {
  // This will be populated when the function is called
  let Document: any, Page: any, Text: any, View: any, StyleSheet: any;
  
  // We need to import these dynamically since this is called from the download function
  const ReactPDF = require('@react-pdf/renderer');
  Document = ReactPDF.Document;
  Page = ReactPDF.Page;
  Text = ReactPDF.Text;
  View = ReactPDF.View;
  StyleSheet = ReactPDF.StyleSheet;

  const { personalInfo, experience, education, skills, template } = data;

  // Get template-specific styles based on selected template
  const getTemplateStyles = (selectedTemplate: string) => {
    const baseStyles = {
      page: {
        flexDirection: 'column',
        backgroundColor: '#FFFFFF',
        padding: '0.75in',
        fontSize: 11,
        fontFamily: 'Helvetica',
        lineHeight: 1.1,
      },
      header: {
        textAlign: 'center',
        marginBottom: 12,
        paddingBottom: 6,
      },
      name: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
        color: '#000000',
        textTransform: 'uppercase',
      },
      contact: {
        fontSize: 11,
        color: '#000000',
        marginBottom: 2,
        lineHeight: 1.1,
      },
      sectionTitle: {
        fontSize: 12,
        fontWeight: 'bold',
        marginTop: 12,
        marginBottom: 6,
        color: '#000000',
        textTransform: 'uppercase',
        letterSpacing: 0.5,
        borderBottom: '1pt solid #000000',
        paddingBottom: 3,
      },
      text: {
        fontSize: 11,
        lineHeight: 1.2,
        marginBottom: 4,
        color: '#000000',
      },
      bulletPoint: {
        fontSize: 11,
        marginBottom: 2,
        marginLeft: 16,
        color: '#000000',
        lineHeight: 1.2,
      },
      experienceHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 3,
        alignItems: 'flex-start',
      },
      jobTitle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#000000',
        marginBottom: 1,
      },
      company: {
        fontSize: 11,
        color: '#000000',
        marginBottom: 1,
      },
      date: {
        fontSize: 11,
        color: '#000000',
        textAlign: 'right',
        fontWeight: 'normal',
      },
      location: {
        fontSize: 11,
        color: '#000000',
        textAlign: 'right',
        marginTop: 1,
      },
      skillsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
      },
      skill: {
        fontSize: 11,
        color: '#000000',
        marginRight: 14,
        marginBottom: 2,
      },
      educationHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 3,
        alignItems: 'flex-start',
      },
      degree: {
        fontSize: 12,
        fontWeight: 'bold',
        color: '#000000',
        marginBottom: 1,
      },
      institution: {
        fontSize: 11,
        color: '#000000',
      },
    };

    // Customize styles based on template
    switch (selectedTemplate) {
      case 'modern':
        return {
          ...baseStyles,
          name: { ...baseStyles.name, color: '#2563eb' },
          sectionTitle: { ...baseStyles.sectionTitle, color: '#2563eb', borderBottom: '2pt solid #2563eb' },
        };
      case 'creative':
        return {
          ...baseStyles,
          name: { ...baseStyles.name, color: '#7c3aed' },
          sectionTitle: { ...baseStyles.sectionTitle, color: '#7c3aed', borderBottom: '2pt solid #7c3aed' },
          header: { ...baseStyles.header, textAlign: 'left' },
        };
      case 'minimal':
        return {
          ...baseStyles,
          sectionTitle: { ...baseStyles.sectionTitle, borderBottom: 'none', borderLeft: '3pt solid #000000', paddingLeft: 8 },
        };
      case 'executive':
        return {
          ...baseStyles,
          name: { ...baseStyles.name, fontSize: 18, color: '#1f2937' },
          sectionTitle: { ...baseStyles.sectionTitle, color: '#1f2937', fontSize: 13 },
        };
      case 'tech':
        return {
          ...baseStyles,
          name: { ...baseStyles.name, color: '#059669' },
          sectionTitle: { ...baseStyles.sectionTitle, color: '#059669', borderBottom: '1pt solid #059669' },
        };
      default:
        return baseStyles;
    }
  };

  const styles = StyleSheet.create(getTemplateStyles(template || 'modern'));


  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.name}>
            {personalInfo.fullName || 'Your Name'}
          </Text>
          <Text style={styles.contact}>
            {personalInfo.phone && `${personalInfo.phone} | `}
            {personalInfo.email && `${personalInfo.email} | `}
            {personalInfo.location && personalInfo.location}
          </Text>
          {personalInfo.linkedin && (
            <Text style={styles.contact}>{personalInfo.linkedin}</Text>
          )}
        </View>

        {/* Summary */}
        {personalInfo.summary && (
          <View>
            <Text style={styles.sectionTitle}>Summary</Text>
            <Text style={styles.text}>{personalInfo.summary}</Text>
          </View>
        )}

        {/* Experience */}
        {experience && experience.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>Experience</Text>
            {experience.map((exp: any, index: number) => (
              <View key={index} style={{ marginBottom: 8 }}>
                <View style={styles.experienceHeader}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.jobTitle}>{exp.jobTitle}</Text>
                    <Text style={styles.company}>{exp.company}</Text>
                  </View>
                  <View>
                    <Text style={styles.date}>
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </Text>
                    <Text style={styles.location}>{exp.location}</Text>
                  </View>
                </View>
                {exp.description && exp.description.map((desc: string, descIndex: number) => (
                  <Text key={descIndex} style={styles.bulletPoint}>
                    • {desc}
                  </Text>
                ))}
              </View>
            ))}
          </View>
        )}

        {/* Education */}
        {education && education.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>Education</Text>
            {education.map((edu: any, index: number) => (
              <View key={index} style={{ marginBottom: 6 }}>
                <View style={styles.educationHeader}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.degree}>{edu.degree}</Text>
                    <Text style={styles.institution}>{edu.institution}</Text>
                  </View>
                  <View>
                    <Text style={styles.date}>{edu.graduationDate}</Text>
                    <Text style={styles.location}>{edu.location}</Text>
                  </View>
                </View>
                {edu.gpa && (
                  <Text style={styles.text}>GPA: {edu.gpa}</Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Skills */}
        {skills && skills.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>Skills</Text>
            <View style={styles.skillsContainer}>
              {skills.map((skill: any, index: number) => (
                <Text key={skill.id || `skill-${index}`} style={styles.skill}>
                  {skill.name || ''}
                </Text>
              ))}
            </View>
          </View>
        )}
      </Page>
    </Document>
  );
}
