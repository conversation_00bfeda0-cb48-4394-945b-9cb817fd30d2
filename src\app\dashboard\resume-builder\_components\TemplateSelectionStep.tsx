"use client";
import { motion } from 'motion/react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { SmartTemplateSelector } from './SmartTemplateSelector';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { careerFields } from './resumeData';

export function TemplateSelectionStep() {
  const { resumeBuilder, populateWithDummyContent } = useGlobalStore();
  const { careerField } = resumeBuilder.data;

  const handlePopulateWithSample = () => {
    if (careerField) {
      populateWithDummyContent(careerField);
    }
  };

  const selectedCareerField = careerFields.find(f => f.id === careerField);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose Your Template</h2>
        <p className="text-sm text-gray-600">
          Select a professional template that best represents your style and career field.
          {careerField && " We've filtered templates that work well for your selected career field."}
        </p>
      </div>

      {/* Smart Template Selector */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <SmartTemplateSelector />
      </div>

      {/* Sample Content Option */}
      {careerField && selectedCareerField && (
        <motion.div
          className="p-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sparkles className="w-6 h-6 text-indigo-600" />
              <div>
                <h3 className="text-lg font-semibold text-indigo-900">Get Started Quickly</h3>
                <p className="text-sm text-indigo-700">
                  Fill your resume with realistic sample content for {selectedCareerField.name}
                </p>
              </div>
            </div>
            <Button
              onClick={handlePopulateWithSample}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Fill with Sample Content
            </Button>
          </div>
          <div className="mt-4 text-xs text-indigo-600">
            <p><strong>Note:</strong> This will populate all sections with sample data appropriate for your career field. You can edit or replace any content afterward.</p>
          </div>
        </motion.div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-900">Template Selection Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Choose a template that matches your industry's expectations</li>
                <li>Creative fields can use more colorful designs</li>
                <li>Traditional industries prefer classic, clean layouts</li>
                <li>You can always change your template later</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
