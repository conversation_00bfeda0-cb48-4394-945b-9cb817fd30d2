"use client";
import { motion } from 'motion/react';
import { useGlobalStore } from '@/store/useGlobalStore';
import { SmartTemplateSelector } from './SmartTemplateSelector';

export function TemplateSelectionStep() {
  const { resumeBuilder } = useGlobalStore();
  const { careerField } = resumeBuilder.data;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose Your Template</h2>
        <p className="text-sm text-gray-600">
          Select a professional template that best represents your style and career field.
          {careerField && " We've filtered templates that work well for your selected career field."}
        </p>
      </div>

      {/* Smart Template Selector */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <SmartTemplateSelector />
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-900">Template Selection Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Choose a template that matches your industry's expectations</li>
                <li>Creative fields can use more colorful designs</li>
                <li>Traditional industries prefer classic, clean layouts</li>
                <li>You can always change your template later</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
