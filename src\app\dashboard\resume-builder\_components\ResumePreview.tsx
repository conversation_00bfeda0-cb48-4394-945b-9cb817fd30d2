"use client";
import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useGlobalStore } from '@/store/useGlobalStore';
import { Download, Eye, X } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { TemplateRenderer } from './TemplateRenderer';



export function ResumePreview() {
  const { resumeBuilder } = useGlobalStore();
  const [isClient, setIsClient] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDownload = async () => {
    // Open the preview modal first, then user can use browser print
    setShowPreview(true);

    // After a short delay, show print dialog
    setTimeout(() => {
      window.print();
    }, 500);
  };

  if (!isClient) {
    return null;
  }

  return (
    <>
      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => setShowPreview(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
        >
          <Eye size={16} />
          Preview
        </button>
        
        <button
          onClick={handleDownload}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
        >
          <Download size={16} />
          Download PDF
        </button>
      </div>

      {/* Preview Modal - Fullscreen with Portal */}
      {showPreview && isClient && createPortal(
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-75 z-[9999] flex flex-col"
          style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
          onClick={() => setShowPreview(false)}
        >
          <motion.div
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            className="w-full h-full bg-white flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-white shadow-sm print:hidden">
              <h3 className="text-lg font-semibold text-gray-900">Resume Preview</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => window.print()}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                >
                  <Download size={16} />
                  Print/Save as PDF
                </button>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-2 rounded-full transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Template Preview Container */}
            <div className="flex-1 bg-gray-50 p-8 print:p-0 print:bg-white">
              <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden print:shadow-none print:max-w-none">
                <div style={{ height: '800px' }} className="w-full print:h-auto">
                  <TemplateRenderer
                    data={resumeBuilder.data}
                    className="w-full h-full"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>,
        document.body
      )}
    </>
  );
}
